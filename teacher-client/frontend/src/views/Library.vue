<template>
  <div class="library-container">
    <div class="library-header">
      <h2 class="library-title">软件库</h2>
      <div class="header-actions">
        <el-button type="primary" @click="refreshPackages" :loading="loading">
          <el-icon><el-icon-refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else-if="packages.length === 0" class="empty-container">
      <el-empty description="没有找到实验包" />
    </div>

    <div v-else class="package-grid">
      <div v-for="pkg in packages" :key="pkg.id" class="package-card">
        <div class="package-cover" :style="generateCoverStyle(pkg)">
          <div class="package-title-display">{{ getDisplayTitle(pkg.experimentName) }}</div>
          <div class="package-status" :class="getStatusClass(pkg.syncStatus)">
            {{ getStatusText(pkg.syncStatus) }}
          </div>
          <!-- 左上角区域：详情按钮和实验版本名称 -->
          <div class="top-left-area">
            <div class="top-left-content">
              <!-- 详情按钮 -->
              <div class="detail-button">
                <el-button
                  type="info"
                  size="small"
                  circle
                  @click="viewDetails(pkg.id)"
                  title="查看详情"
                >
                  <el-icon><el-icon-info-filled /></el-icon>
                </el-button>
              </div>
              <!-- 实验版本名称 -->
              <div v-if="pkg.experimentVersionName">
                <el-tag size="small" type="warning">{{ pkg.experimentVersionName }}</el-tag>
              </div>
            </div>
          </div>
        </div>
        <div class="package-info">
          <!-- <h3 class="package-title">{{ pkg.experimentName }}</h3> -->
          <div class="package-version">
            <div class="version-info-row">
              <span class="version-name">{{ pkg.versionName }}</span>
              <span class="version-number">v{{ pkg.version }}</span>
            </div>
          </div>
          <div class="package-actions">
            <template v-if="pkg.syncStatus === 4">
              <!-- 已完成状态显示运行按钮和更新按钮 -->
              <div v-if="pkg.hasUpdate" class="dual-buttons">
                <el-button
                  type="success"
                  @click.stop="runPackage(pkg.id)"
                  class="action-button"
                  size="large"
                >
                  运行
                </el-button>
                <el-button
                  type="warning"
                  @click.stop="updatePackage(pkg.id)"
                  class="action-button"
                  size="large"
                >
                  更新
                </el-button>
              </div>
              <div v-else class="dual-buttons">
                <el-button
                  type="success"
                  @click.stop="runPackage(pkg.id)"
                  class="action-button"
                  size="large"
                >
                  运行
                </el-button>
                <el-button
                  type="danger"
                  @click.stop="deletePackageDownload(pkg.id)"
                  class="action-button delete-button"
                  size="large"
                  title="删除下载文件，释放存储空间"
                >
                  删除
                </el-button>
              </div>
            </template>
            <template v-else-if="pkg.syncStatus === 2">
              <!-- 已下载但未解压状态显示解压按钮和更新按钮 -->
              <div v-if="pkg.hasUpdate" class="dual-buttons">
                <el-button
                  type="primary"
                  @click.stop="extractPackage(pkg.id)"
                  class="action-button"
                  size="large"
                >
                  解压
                </el-button>
                <el-button
                  type="warning"
                  @click.stop="updatePackage(pkg.id)"
                  class="action-button"
                  size="large"
                >
                  更新
                </el-button>
              </div>
              <div v-else class="triple-buttons">
                <el-button
                  type="primary"
                  @click.stop="extractPackage(pkg.id)"
                  class="action-button"
                  size="large"
                >
                  解压
                </el-button>
                <el-button
                  type="danger"
                  @click.stop="deletePackageDownload(pkg.id)"
                  class="action-button delete-button"
                  size="small"
                  title="删除下载文件，释放存储空间"
                >
                  删除
                </el-button>
              </div>
            </template>
            <template v-else-if="pkg.syncStatus === 0">
              <!-- 未下载状态显示下载按钮 -->
              <el-button
                type="primary"
                @click.stop="downloadPackage(pkg.id)"
                class="action-button"
                size="large"
              >
                下载
              </el-button>
            </template>
            <template v-else-if="pkg.syncStatus === 1">
              <!-- 下载中状态显示下载中按钮 -->
              <el-button
                type="info"
                disabled
                class="action-button"
                size="large"
              >
                下载中
              </el-button>
            </template>
            <template v-else-if="pkg.syncStatus === 3">
              <!-- 解压中状态显示解压中按钮和取消按钮 -->
              <div class="dual-buttons">
                <el-button
                  type="info"
                  disabled
                  class="action-button"
                  size="large"
                >
                  解压中
                </el-button>
                <el-button
                  type="warning"
                  @click.stop="cancelExtractForPackage(pkg.id)"
                  class="action-button cancel-button"
                  size="small"
                >
                  取消
                </el-button>
              </div>
            </template>
            <template v-else-if="pkg.syncStatus === 5">
              <!-- 失败状态显示重试按钮 -->
              <el-button
                type="danger"
                @click.stop="downloadPackage(pkg.id)"
                class="action-button"
                size="large"
              >
                重试
              </el-button>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 更新日志对话框 -->
    <el-dialog
      v-model="changelogDialogVisible"
      :title="changelogTitle"
      width="600px"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :show-close="true"
    >
      <div class="changelog-content">
        <div class="experiment-info">
          <h2 class="experiment-name">{{ selectedPackage.experimentName }}</h2>
          <div class="experiment-version">{{ selectedPackage.experimentVersionName || selectedPackage.versionName }}</div>
        </div>

        <div class="version-info">
          <div class="version-header">
            <div class="version-details">
              <h3>{{ selectedPackage.versionName }}</h3>
              <span class="version-number">v{{ selectedPackage.version }}</span>
            </div>
            <el-tag size="small" type="success">{{ actionType === 'update' ? '最新版本' : '当前版本' }}</el-tag>
          </div>
          <div class="version-date">发布日期: {{ formatDate(selectedPackage.releaseDate || new Date()) }}</div>
        </div>

        <el-divider content-position="left">{{ actionType === 'update' ? '更新内容' : '版本说明' }}</el-divider>

        <div class="changelog-details" v-html="formatChangelog(selectedPackage.versionDesc || selectedPackage.changelog || '暂无更新日志')"></div>

        <div class="file-info">
          <div class="file-size">文件大小: {{ formatFileSize(selectedPackage.fileSize || 15728640) }}</div>
          <div class="file-hash">文件校验: {{ selectedPackage.fileHash || 'SHA256:e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855' }}</div>
        </div>

        <div class="changelog-actions">
          <el-button
            type="primary"
            size="large"
            @click="confirmDownload"
          >
            {{ actionType === 'update' ? '更新' : '下载' }}
          </el-button>
          <el-button
            @click="changelogDialogVisible = false"
            size="large"
          >
            取消
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 下载进度对话框 -->
    <el-dialog
      v-model="downloadDialogVisible"
      title="下载进度"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="download-progress">
        <div class="progress-info">
          <span>{{ downloadingPackage.experimentName }} - {{ downloadingPackage.versionName }}</span>
          <span>{{ formatFileSize(downloadProgress.downloaded) }} / {{ formatFileSize(downloadProgress.total) }}</span>
        </div>
        <div class="speed-info" v-if="downloadProgress.speed > 0">
          <span>{{ downloadProgress.filename }}</span>
          <span>{{ formatFileSize(downloadProgress.speed) }}/s</span>
        </div>
        <el-progress
          :percentage="downloadProgress.percentage"
          :status="downloadStatus"
        />
        <div class="download-actions">
          <el-button
            v-if="downloadStatus === 'success'"
            type="primary"
            @click="closeDownloadDialog"
          >
            完成
          </el-button>
          <template v-else-if="downloadStatus === 'exception'">
            <el-button
              type="danger"
              @click="retryDownload"
            >
              重试
            </el-button>
            <el-button
              type="info"
              @click="closeDownloadDialog"
            >
              关闭
            </el-button>
          </template>
          <el-button
            v-else
            type="warning"
            @click="cancelDownload"
          >
            取消
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 解压进度对话框 -->
    <el-dialog
      v-model="extractDialogVisible"
      title="解压进度"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="extract-progress">
        <div class="progress-info">
          <span>正在解压文件...</span>
          <span>{{ extractProgress.extracted }} / {{ extractProgress.total }}</span>
        </div>
        <div class="file-info" v-if="extractProgress.currentFile">
          <span>{{ extractProgress.currentFile }}</span>
        </div>
        <div class="percentage-info">
          <span>{{ extractProgress.percentage.toFixed(2) }}%</span>
        </div>
        <el-progress
          :percentage="extractProgress.percentage"
        />
        <div class="extract-note" v-if="extractProgress.percentage > 0 && extractProgress.percentage < 100">
          <p>解压过程可能需要几分钟，请耐心等待...</p>
          <p>如果长时间没有进度更新，可能是在处理大文件</p>
        </div>
        <div class="extract-actions">
          <el-button
            type="danger"
            @click="cancelExtract"
            v-if="extractProgress.percentage < 100"
          >
            取消解压
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 同步进度对话框 -->
    <el-dialog
      v-model="syncDialogVisible"
      title="同步进度"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="sync-progress">
        <div class="progress-info">
          <span>{{ syncProgress.currentTask }}</span>
          <span>{{ syncProgress.completedTasks }} / {{ syncProgress.totalTasks }}</span>
        </div>
        <el-progress
          :percentage="syncProgress.percentage"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { handleAuthExpired, isAuthExpiredError, isServerStatusAuthExpired, wrapApiCall } from '@/utils/auth'

export default {
  name: 'Library',
  data() {
    return {
      packages: [],
      loading: false,
      changelogDialogVisible: false,
      changelogTitle: '更新日志',
      selectedPackage: {},
      actionType: 'download', // 'download' or 'update'
      downloadDialogVisible: false,
      downloadingPackage: {},
      downloadProgress: {
        total: 0,
        downloaded: 0,
        percentage: 0,
        speed: 0,
        filename: ''
      },
      downloadStatus: 'primary', // primary, success, exception
      extractDialogVisible: false,
      extractProgress: {
        total: 0,
        extracted: 0,
        percentage: 0,
        currentFile: ''
      },
      currentExtractID: null,
      extractProgressMonitor: null,
      extractTimeoutTimer: null,
      extractFinalTimeoutTimer: null,
      extractID: null,
      syncDialogVisible: false,
      syncProgress: {
        percentage: 0,
        currentTask: '',
        completedTasks: 0,
        totalTasks: 3
      },
      // 用于生成背景的颜色组合
      colorPalettes: [
        { primary: '#4A90E2', secondary: '#5AAAFA', text: '#FFFFFF' }, // 蓝色系
        { primary: '#50E3C2', secondary: '#86E2D5', text: '#FFFFFF' }, // 青色系
        { primary: '#F5A623', secondary: '#F8C471', text: '#FFFFFF' }, // 橙色系
        { primary: '#8B572A', secondary: '#B18F6A', text: '#FFFFFF' }, // 棕色系
        { primary: '#7ED321', secondary: '#9CCC65', text: '#FFFFFF' }, // 绿色系
        { primary: '#BD10E0', secondary: '#D471E3', text: '#FFFFFF' }, // 紫色系
        { primary: '#E91E63', secondary: '#F06292', text: '#FFFFFF' }, // 粉色系
        { primary: '#9013FE', secondary: '#B39DDB', text: '#FFFFFF' }, // 紫罗兰色系
        { primary: '#417505', secondary: '#7CB342', text: '#FFFFFF' }, // 深绿色系
        { primary: '#D0021B', secondary: '#EF5350', text: '#FFFFFF' }  // 红色系
      ]
    }
  },
  mounted() {
    this.loadPackages()
    this.getDownloads()
  },

  beforeUnmount() {
    // 清理解压相关资源
    this.cleanupExtractResources()
  },

  watch: {
    // 监听解压对话框关闭
    extractDialogVisible(newVal) {
      if (!newVal) {
        // 对话框关闭时清理资源
        this.cleanupExtractResources()
      }
    }
  },
  methods: {
    async fetchPackages() {
      this.loading = true

      try {
        // 先检查服务器状态
        const status = await wrapApiCall(window.go.main.App.GetServerStatus, '')

        // 检查是否为授权过期或需要登录
        if (isServerStatusAuthExpired(status) || status.status === 'need_login') {
          handleAuthExpired()
          return
        }

        if (status.status !== 'online') {
          // 服务器离线时，只加载本地包，不尝试同步
          this.$message.warning('服务器未连接，只显示已下载的包')
          const localPackages = await wrapApiCall(window.go.main.App.GetPackages)
          this.packages = localPackages || []
        } else {
          // 服务器在线，触发同步
          const syncProgressKey = await window.go.main.App.SyncNow()

          // 监听同步进度
          window.runtime.EventsOn(syncProgressKey, (progress) => {
            if (progress.error) {
              this.$message.error('同步失败: ' + progress.error)
              this.loading = false
              window.runtime.EventsOff(syncProgressKey)
              return
            }

            if (progress.percentage >= 100) {
              this.loadPackages()
              window.runtime.EventsOff(syncProgressKey)
            }
          })
        }
      } catch (error) {
        console.error('刷新包列表失败:', error)
        this.$message.error('刷新失败: ' + error.toString())
        this.loading = false
      }
    },
    async checkPackageUpdates() {
      // 检查已下载包是否有更新
      for (let i = 0; i < this.packages.length; i++) {
        const pkg = this.packages[i]
        // 只检查已下载或已完成的包
        if (pkg.syncStatus === 2 || pkg.syncStatus === 4) {
          try {
            const updateInfo = await window.go.main.App.CheckPackageUpdates(pkg.id)
            // 将更新信息添加到包对象中
            this.packages [i]= {
              ...pkg,
              hasUpdate: updateInfo.hasUpdate,
              updatePackage: updateInfo.updatePackage
            }
          } catch (error) {
            console.error(`检查包 ${pkg.id} 更新失败:`, error)
            // 如果检查更新失败，设置为没有更新
            this.packages[i]={
              ...pkg,
              hasUpdate: false
            }
          }
        }
      }
    },
    async refreshPackages() {
      try {
        // 先检查服务器状态
        const status = await wrapApiCall(window.go.main.App.GetServerStatus, '')

        // 检查是否为授权过期或需要登录
        if (isServerStatusAuthExpired(status) || status.status === 'need_login') {
          handleAuthExpired()
          return
        }

        if (status.status !== 'online') {
          // 服务器离线时，只提示需要连接服务器，不影响已显示的包列表
          this.$message.warning('服务器未连接，请先在设置页面配置并连接服务器')
          return // 直接返回，不重新加载
        }

        // 服务器在线时才执行实际的刷新操作
        this.loading = true

        // 触发同步
        const syncProgressKey = await window.go.main.App.SyncNow()

        // 监听同步进度
        window.runtime.EventsOn(syncProgressKey, (progress) => {
          if (progress.error) {
            this.$message.error('同步失败')
            this.loading = false
            window.runtime.EventsOff(syncProgressKey)
            return
          }

          if (progress.percentage >= 100) {
            this.loadPackages()
            window.runtime.EventsOff(syncProgressKey)
          }
        })
      } catch (error) {
        console.error('刷新包列表失败:', error)
        this.$message.error('刷新失败')
        this.loading = false
      }
    },
    // 生成封面样式
    generateCoverStyle(pkg) {
      // 如果有封面图片，直接使用
      if (pkg.coverImage) {
        return { backgroundImage: `url(${pkg.coverImage})` }
      }

      // 否则，根据实验名称生成渐变背景
      // 使用实验名称的哈希值来选择颜色组合
      const hash = this.hashString(pkg.experimentName)
      const colorIndex = hash % this.colorPalettes.length
      const colors = this.colorPalettes[colorIndex]

      // 生成渐变背景
      return {
        background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%)`,
        color: colors.text
      }
    },
    // 获取显示标题
    getDisplayTitle(experimentName) {
      if (!experimentName) return ''

      // 返回完整的实验名称，让CSS处理换行
      return experimentName
    },
    // 简单的字符串哈希函数
    hashString(str) {
      let hash = 0
      for (let i = 0; i < str.length; i++) {
        hash = ((hash << 5) - hash) + str.charCodeAt(i)
        hash = hash & hash // 转换为32位整数
      }
      return Math.abs(hash)
    },
    getStatusClass(status) {
      const classes = {
        0: 'status-not-downloaded',  // 未同步
        1: 'status-downloading',     // 下载中
        2: 'status-downloaded',      // 已下载
        3: 'status-extracting',      // 解压中
        4: 'status-completed',       // 已完成
        5: 'status-failed'           // 失败
      }
      return classes[status] || 'status-not-downloaded'
    },
    getStatusText(status) {
      const texts = {
        0: '未下载',
        1: '下载中',
        2: '已下载',
        3: '解压中',
        4: '可运行',
        5: '失败'
      }
      return texts[status] || '未知'
    },
    viewDetails(id) {
      this.$router.push(`/package/${id}`)
    },
    async runPackage(id) {
      try {
        // 调用Go后端运行包
        await window.go.main.App.RunPackage(id)
        this.$message.success('应用启动成功')
      } catch (error) {
        console.error('启动应用失败:', error)
        this.$message.error('启动应用失败: ' + error.toString())
      }
    },
    async downloadPackage(id) {
      const pkg = this.packages.find(p => p.id === id)
      if (!pkg) return

      // 设置选中的包和操作类型
      this.selectedPackage = pkg
      this.actionType = 'download'
      this.changelogTitle = '下载信息'

      // 显示更新日志对话框
      this.changelogDialogVisible = true
    },
    updatePackage(id) {
      const pkg = this.packages.find(p => p.id === id)
      if (!pkg || !pkg.hasUpdate || !pkg.updatePackage) return

      // 设置选中的包为更新包的信息，但保留当前包的ID用于后续操作
      this.selectedPackage = {
        ...pkg.updatePackage,
        currentPackageId: pkg.id // 保存当前包的ID
      }
      this.actionType = 'update'
      this.changelogTitle = '更新日志'

      // 显示更新日志对话框
      this.changelogDialogVisible = true
    },
    async confirmDownload() {
      // 关闭更新日志对话框
      this.changelogDialogVisible = false

      try {
        // 设置下载包和进度
        this.downloadingPackage = this.selectedPackage
        this.downloadDialogVisible = true
        this.downloadProgress = {
          total: this.selectedPackage.fileSize || 100000000, // 使用包大小或默认100MB
          downloaded: 0,
          percentage: 0,
          speed: 0,
          filename: ''
        }
        this.downloadStatus = 'primary'

        // 调用Go后端下载，获取下载ID
        const downloadID = await window.go.main.App.DownloadPackage(this.selectedPackage.id)
        console.log('获取到下载ID:', downloadID)

        // 处理进度更新
        const handleProgress = (progress) => {
          console.log('收到下载进度更新:', progress)

          // 检查是否有错误
          if (progress.error) {
            console.error('下载错误:', progress.error)
            this.$message.error('下载错误: ' + progress.error)
            this.downloadStatus = 'exception'

            // 回退包状态到未下载
            this.revertPackageStatusOnDownloadError()

            // 取消事件监听
            window.runtime.EventsOff(downloadID)
            return
          }

          // 更新进度
          this.downloadProgress = progress
          console.log('更新前端进度显示:', progress.percentage + '%')

          // 检查是否完成
          if (progress.completed || this.downloadProgress.percentage === 100) {
            this.downloadStatus = 'success'

            // 更新包状态
            if (this.actionType === 'update' && this.selectedPackage.currentPackageId) {
              // 更新操作：更新原包的状态，并刷新包列表以显示新版本
              const index = this.packages.findIndex(p => p.id === this.selectedPackage.currentPackageId)
              if (index !== -1) {
                this.packages[index].syncStatus = 2 // 已下载
                this.packages[index].hasUpdate = false // 清除更新标记
              }
            } else {
              // 下载操作：更新当前包的状态
              const index = this.packages.findIndex(p => p.id === this.selectedPackage.id)
              if (index !== -1) {
                this.packages[index].syncStatus = 2 // 已下载
              }
            }

            // 关闭下载对话框
            this.downloadDialogVisible = false

            // 自动开始解压（只调用一次）
            const extractPackageId = this.selectedPackage.id

            // 更新包状态为已下载，避免重复触发解压
            const pkgIndex = this.packages.findIndex(p => p.id === extractPackageId)
            if (pkgIndex !== -1) {
              // 只有当包状态不是解压中(3)时才开始解压
              if (this.packages[pkgIndex].syncStatus !== 3) {
                this.extractPackage(extractPackageId)
              }
            } else {
              this.extractPackage(extractPackageId)
            }

            // 取消事件监听
            window.runtime.EventsOff(downloadID)
          }
        }

        // 监听进度更新事件
        console.log('开始监听下载进度事件:', downloadID)
        window.runtime.EventsOn(downloadID, handleProgress)
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败: ' + error.toString())
        this.downloadStatus = 'exception'
      }
    },
    closeDownloadDialog() {
      // 如果下载失败，回退包状态
      if (this.downloadStatus === 'exception') {
        this.revertPackageStatusOnDownloadError()
      }
      this.downloadDialogVisible = false
    },
    retryDownload() {
      // 重置下载状态
      this.downloadStatus = 'primary'
      this.downloadProgress = {
        total: this.downloadingPackage.fileSize || 100000000,
        downloaded: 0,
        percentage: 0,
        speed: 0,
        filename: ''
      }

      // 重新开始下载
      this.confirmDownload()
    },
    async cancelDownload() {
      try {
        // 调用Go后端取消下载
        await window.go.main.App.CancelDownload(this.downloadingPackage.id)

        // 回退包状态到未下载
        this.revertPackageStatusOnDownloadError()

        this.downloadDialogVisible = false
        this.$message.info('下载已取消')
      } catch (error) {
        console.error('取消下载失败:', error)
        this.$message.error('取消下载失败: ' + error.toString())
      }
    },
    async extractPackage(id) {
      try {
        // 检查是否已经在解压中
        if (this.extractDialogVisible && this.currentExtractID) {
          console.log("已有解压任务在进行中，忽略重复调用")
          // 不再显示重复的警告提示，只在控制台打印日志
          return
        }

        // 检查包状态
        const pkg = this.packages.find(p => p.id === id)
        if (pkg && pkg.syncStatus === 3) {
          console.log("包已经在解压中，忽略重复调用")
          // 不再显示重复的警告提示，只在控制台打印日志
          return
        }

        // 设置解压进度对话框
        this.extractDialogVisible = true
        this.extractProgress = {
          total: 0,
          extracted: 0,
          percentage: 0,
          currentFile: ''
        }

        // 调用Go后端解压，获取解压ID
        const extractID = await window.go.main.App.ExtractPackage(id)
        console.log("解压ID:", extractID)

        // 保存当前解压ID
        this.currentExtractID = extractID

        // 设置超时计时器
        let lastProgressTime = Date.now()

        // 设置进度监控计时器
        const progressMonitor = setInterval(() => {
          const now = Date.now()
          const elapsed = now - lastProgressTime

          // 如果超过30秒没有进度更新，显示提示
          if (elapsed > 30000) {
            console.log("解压进度超过30秒没有更新")
            this.$message.warning('解压进度较慢，请耐心等待...')

            // 重置计时器
            lastProgressTime = now
          }
        }, 10000) // 每10秒检查一次

        // 保存计时器引用到组件实例
        this.extractProgressMonitor = progressMonitor

        // 设置超时计时器，3分钟后如果还没完成，显示提示
        const timeoutTimer = setTimeout(() => {
          console.log("解压操作超时")
          this.$message.warning('解压操作时间较长，可能遇到了问题，但仍在继续...')

          // 再设置一个5分钟的超时，如果还没完成，提供重置选项
          const finalTimeoutTimer = setTimeout(() => {
            if (this.extractDialogVisible && this.extractProgress.percentage < 100) {
              console.log("解压操作超过5分钟，可能失败")
              this.$confirm('解压操作时间过长，可能已失败。是否重置包状态？', '解压超时', {
                confirmButtonText: '重置状态',
                cancelButtonText: '继续等待',
                type: 'warning'
              }).then(() => {
                // 重置包状态为已下载
                const index = this.packages.findIndex(p => p.id === id)
                if (index !== -1) {
                  this.packages[index].syncStatus = 2 // 已下载
                }

                this.extractDialogVisible = false
                this.cleanupExtractResources()
                this.$message.info('包状态已重置为已下载，您可以重新尝试解压')
              }).catch(() => {
                // 继续等待
                console.log("用户选择继续等待")
              })
            }
          }, 2 * 60 * 1000) // 再等2分钟，总共5分钟

          // 保存最终超时计时器引用
          this.extractFinalTimeoutTimer = finalTimeoutTimer
        }, 3 * 60 * 1000) // 3分钟

        // 保存超时计时器引用到组件实例
        this.extractTimeoutTimer = timeoutTimer

        // 处理进度更新
        const handleProgress = (progress) => {
          // 更新最后进度时间
          lastProgressTime = Date.now()

          // 检查是否有错误
          if (progress.error) {
            console.error('解压错误:', progress.error)
            this.$message.error('解压错误: ' + progress.error)
            this.extractDialogVisible = false

            // 清除计时器
            clearInterval(progressMonitor)
            clearTimeout(timeoutTimer)

            // 取消事件监听
            try {
              window.runtime.EventsOff(extractID)
            } catch (e) {
              console.log("清除监听器失败:", e)
            }
            return
          }

          // 更新进度
          this.extractProgress = progress
          console.log("解压进度更新:", progress)

          // 检查是否完成
          if (progress.completed || this.extractProgress.percentage >= 99.5) {
            console.log("解压完成，关闭对话框")

            // 防止重复处理完成事件
            if (this.extractDialogVisible) {
              // 清除所有计时器
              this.cleanupExtractResources()

              // 关闭解压对话框
              this.extractDialogVisible = false

              // 显示成功提示
              this.$message.success('解压完成')

              // 更新包状态为已完成，避免重复解压
              const pkgIndex = this.packages.findIndex(p => p.id === id)
              if (pkgIndex !== -1) {
                this.packages[pkgIndex].syncStatus = 4 // 已完成
              }

              // 粗暴刷新页面
              setTimeout(() => {
                window.location.reload()
              }, 1000)
            }
          }
        }

        // 先清除可能存在的旧监听器
        try {
          window.runtime.EventsOff(extractID)
        } catch (e) {
          console.log("清除旧监听器失败:", e)
        }

        // 监听进度更新事件
        console.log("开始监听解压进度事件:", extractID)
        window.runtime.EventsOn(extractID, handleProgress)

        // 保存解压ID引用
        this.extractID = extractID
      } catch (error) {
        console.error('解压失败:', error)
        if(error.toString()!=="包正在解压中，请勿重复操作"){ // 跳过重复解压提示
          this.$message.error('解压失败: ' + error.toString())
        }
        this.extractDialogVisible = false
      }
    },
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    formatDate(date) {
      if (!date) return '未知'
      const d = new Date(date)
      return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },
    formatChangelog(changelog) {
      if (!changelog || changelog === '暂无更新日志') {
        return '<p>暂无更新日志</p>'
      }

      // 如果更新日志是纯文本，将其转换为HTML格式
      if (!changelog.includes('<')) {
        // 将换行符转换为<br>标签
        changelog = changelog.replace(/\n/g, '<br>')

        // 将以"-"或"*"开头的行转换为列表项
        changelog = changelog.replace(/^[*-]\s+(.+)$/gm, '<li>$1</li>')

        // 如果有列表项，将它们包装在<ul>标签中
        if (changelog.includes('<li>')) {
          changelog = '<ul>' + changelog + '</ul>'
        }

        // 将以"#"开头的行转换为标题
        changelog = changelog.replace(/^#{1,6}\s+(.+)$/gm, '<h4>$1</h4>')
      }

      return changelog
    },

    // 清理解压相关资源
    cleanupExtractResources() {
      console.log("清理解压相关资源")

      // 清除进度监控计时器
      if (this.extractProgressMonitor) {
        clearInterval(this.extractProgressMonitor)
        this.extractProgressMonitor = null
        console.log("已清除进度监控计时器")
      }

      // 清除超时计时器
      if (this.extractTimeoutTimer) {
        clearTimeout(this.extractTimeoutTimer)
        this.extractTimeoutTimer = null
        console.log("已清除超时计时器")
      }

      // 清除最终超时计时器
      if (this.extractFinalTimeoutTimer) {
        clearTimeout(this.extractFinalTimeoutTimer)
        this.extractFinalTimeoutTimer = null
        console.log("已清除最终超时计时器")
      }

      // 取消事件监听
      if (this.extractID) {
        try {
          window.runtime.EventsOff(this.extractID)
          console.log("已清除事件监听:", this.extractID)
        } catch (e) {
          console.log("清除监听器失败:", e)
        }
        this.extractID = null
      }

      // 重置当前解压ID
      this.currentExtractID = null
    },

    // 取消解压
    cancelExtract() {
      if (!this.currentExtractID) {
        this.$message.warning('没有正在进行的解压任务')
        return
      }

      this.$confirm('确定要取消解压吗？', '取消解压', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 关闭解压对话框
        this.extractDialogVisible = false

        // 清理资源
        this.cleanupExtractResources()

        this.$message.info('已取消解压')

        // 刷新包列表
        this.fetchPackages()
      }).catch(() => {
        // 用户取消了操作
      })
    },

    // 为特定包取消解压
    async cancelExtractForPackage(packageId) {
      try {
        this.$confirm('确定要取消解压吗？解压将被中断，包状态将重置为已下载。', '取消解压', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          // 调用后端取消解压
          await window.go.main.App.CancelExtract(packageId)

          // 更新包状态为已下载
          const index = this.packages.findIndex(p => p.id === packageId)
          if (index !== -1) {
            this.packages[index].syncStatus = 2 // 已下载
          }

          this.$message.info('已取消解压')

          // 如果当前正在显示解压对话框，关闭它
          if (this.extractDialogVisible) {
            this.extractDialogVisible = false
            this.cleanupExtractResources()
          }
        }).catch(() => {
          // 用户取消了操作
        })
      } catch (error) {
        console.error('取消解压失败:', error)
        this.$message.error('取消解压失败: ' + error.toString())
      }
    },

    // 回退包状态到未下载（下载失败时使用）
    revertPackageStatusOnDownloadError() {
      if (this.actionType === 'update' && this.selectedPackage.currentPackageId) {
        // 更新操作：回退原包的状态
        const index = this.packages.findIndex(p => p.id === this.selectedPackage.currentPackageId)
        if (index !== -1) {
          // 如果原来有下载的版本，保持已下载状态，否则回退到未下载
          const originalStatus = this.packages[index].syncStatus
          if (originalStatus === 1) { // 如果当前是下载中状态，回退到未下载
            this.packages[index].syncStatus = 0 // 未下载
          }
        }
      } else {
        // 下载操作：回退当前包的状态
        const index = this.packages.findIndex(p => p.id === this.selectedPackage.id)
        if (index !== -1) {
          this.packages[index].syncStatus = 0 // 未下载
        }
      }
    },
    async loadPackages() {
      try {
        this.loading = true;

        // 从Go后端获取包列表
        const result = await wrapApiCall(window.go.main.App.GetPackages)
        this.packages = result || []

        console.log("加载到包列表:", this.packages.length, "个包")

        // 检查已下载包是否有更新
        await this.checkPackageUpdates()

        // 如果没有包，显示提示
        if (this.packages.length === 0) {
          console.log("没有找到实验包")
          this.$message.info('没有找到实验包，请点击刷新按钮同步')

          // 检查服务器状态，如果不在线尝试显示更明确的提示
          try {
            const status = await wrapApiCall(window.go.main.App.GetServerStatus, '')

            // 检查是否为授权过期或需要登录
            if (isServerStatusAuthExpired(status) || status.status === 'need_login') {
              handleAuthExpired()
              return
            }

            if (status.status !== 'online') {
              this.$message.warning('服务器未连接，请先在设置页面配置并连接服务器')
            }
          } catch (error) {
            console.error('获取服务器状态失败:', error)
          }
        }
      } catch (error) {
        // 检查是否为授权过期错误
        if (isAuthExpiredError(error)) {
          handleAuthExpired()
          return
        }

        console.error('获取包列表失败:', error)
        this.$message.error('获取包列表失败')
        this.packages = []
      } finally {
        this.loading = false
      }
    },
    async getDownloads() {
      try {
        // 获取下载列表，用于更新下载状态
        const downloads = await window.go.main.App.GetDownloads()
        console.log("获取下载列表:", downloads)

        // 这里可以处理下载列表，但保留方法以便调用
      } catch (error) {
        console.error('获取下载列表失败:', error)
      }
    },
    async deletePackageDownload(packageId) {
      try {
        await this.$confirm('确定要删除此包的下载文件吗？这将释放存储空间，但您需要重新下载才能使用。', '删除下载', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用后端删除下载文件
        await window.go.main.App.DeletePackageDownload(packageId)

        this.$message.success('下载文件已删除，存储空间已释放')

        // 刷新包列表
        this.loadPackages()
      } catch (error) {
        if (error === 'cancel') {
          // 用户取消了操作
          return
        }
        console.error('删除下载文件失败:', error)
        this.$message.error('删除下载文件失败: ' + error.toString())
      }
    }
  }
}
</script>

<style scoped>
.library-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.library-title {
  font-size: 22px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.3px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.header-actions .el-button {
  border-radius: 8px !important;
  padding: 8px 16px !important;
  font-weight: 500 !important;
  letter-spacing: 0.3px !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-size: 14px !important;
}

.header-actions .el-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25) !important;
}

.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.package-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 280px;
  border: 1px solid #f0f0f0;
}

.package-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  border-color: #e0e6ed;
}

.package-cover {
  height: 140px;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-top: 20px;
}

.package-title-display {
  font-size: 23px;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  word-break: break-word;
  line-height: 1.4;
  max-width: 90%;
  padding: 0 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: white;
  letter-spacing: 0.5px;
}

.package-status {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 11px;
  color: white;
  font-weight: 600;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.top-left-area {
  position: absolute;
  top: 12px;
  left: 12px;
  display: flex;
  z-index: 2;
}

.top-left-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-button {
  opacity: 0.9;
  transition: all 0.3s ease;
}

.package-card:hover .detail-button {
  opacity: 1;
  transform: scale(1.05);
}

.detail-button .el-button {
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
  color: white;
  backdrop-filter: blur(4px);
}

.detail-button .el-button:hover {
  background-color: rgba(0, 0, 0, 0.85);
  transform: scale(1.1);
}

.status-not-downloaded {
  background: linear-gradient(135deg, #909399, #a6a9ad);
}

.status-downloading {
  background: linear-gradient(135deg, #e6a23c, #f0b90b);
  animation: pulse 2s infinite;
}

.status-downloaded {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.status-extracting {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  animation: pulse 2s infinite;
}

.status-completed {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  box-shadow: 0 0 12px rgba(103, 194, 58, 0.3);
}

.status-failed {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  animation: shake 0.5s ease-in-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.95);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.package-info {
  padding: 16px 18px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: linear-gradient(to bottom, #ffffff 0%, #fafbfc 100%);
}

.package-version {
  margin-bottom: 14px;
  flex-shrink: 0;
}

.version-info-row {
  display: flex;
  align-items: baseline;
  gap: 12px;
  justify-content: space-between;
}

.version-name {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
  line-height: 1.3;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.version-number {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  background-color: #f5f7fa;
  padding: 3px 8px;
  border-radius: 12px;
  display: inline-block;
  flex-shrink: 0;
  border: 1px solid #e4e7ed;
}

.package-actions {
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding-top: 10px;
}

.action-button {
  width: 100%;
  height: 38px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px;
  border-radius: 8px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.action-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15) !important;
}

.dual-buttons {
  display: flex;
  width: 100%;
  gap: 10px;
}

.dual-buttons .el-button {
  flex: 1;
  height: 38px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
}

.dual-buttons .delete-button {
  flex: 0 0 auto !important;
  width: 60px !important;
  min-width: 60px !important;
  padding: 0 8px !important;
  font-size: 12px !important;
}

.triple-buttons {
  display: flex;
  width: 100%;
  gap: 8px;
}

.triple-buttons .el-button {
  flex: 1;
  height: 38px !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
}

.triple-buttons .delete-button {
  flex: 0 0 auto !important;
  width: 50px !important;
  min-width: 50px !important;
  padding: 0 6px !important;
  font-size: 11px !important;
}

.cancel-button {
  flex: 0 0 auto !important;
  width: auto !important;
  min-width: 60px !important;
  padding: 0 12px !important;
}

.delete-button {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
  color: white !important;
}

.delete-button:hover {
  background-color: #f78989 !important;
  border-color: #f78989 !important;
}

.loading-container, .empty-container {
  padding: 80px 40px;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  margin: 20px;
  backdrop-filter: blur(8px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.empty-container .el-empty {
  --el-empty-description-margin-top: 20px;
}

.empty-container .el-empty__description {
  font-size: 16px;
  color: #718096;
  font-weight: 500;
}

.changelog-content {
  padding: 10px;
}

.experiment-info {
  margin-bottom: 20px;
  text-align: center;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 15px;
}

.experiment-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.experiment-version {
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

.version-info {
  margin-bottom: 20px;
}

.version-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}

.version-details {
  display: flex;
  align-items: baseline;
  gap: 10px;
}

.version-details h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.version-details .version-number {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
}

.version-date {
  font-size: 14px;
  color: #909399;
}

.changelog-details {
  margin: 20px 0;
  line-height: 1.6;
  color: #606266;
}

.changelog-details ul {
  padding-left: 20px;
}

.changelog-details li {
  margin-bottom: 8px;
}

.changelog-details h4 {
  margin: 15px 0 10px 0;
  font-size: 16px;
  color: #303133;
}

.file-info {
  background-color: #f5f7fa;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 13px;
  color: #606266;
  font-family: monospace;
}

.file-size {
  margin-bottom: 5px;
}

.changelog-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.download-progress, .extract-progress {
  padding: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.progress-info span:first-child {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
  flex: 1;
  margin-right: 12px;
  word-break: break-word;
}

.progress-info span:last-child {
  color: #606266;
  font-size: 13px;
  font-family: 'Courier New', monospace;
  background-color: #e7f4ff;
  padding: 4px 8px;
  border-radius: 4px;
  white-space: nowrap;
}

.speed-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border-radius: 4px;
  font-size: 12px;
}

.speed-info span:first-child {
  color: #606266;
  flex: 1;
  margin-right: 12px;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.speed-info span:last-child {
  color: #67c23a;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
}

.file-info {
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #fafafa;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  word-break: break-all;
  border-left: 3px solid #e6a23c;
}

.percentage-info {
  text-align: center;
  margin: 15px 0;
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.extract-note {
  margin: 20px 0;
  padding: 12px;
  background-color: #fff7e6;
  border-radius: 6px;
  font-size: 13px;
  color: #e6a23c;
  border-left: 4px solid #faad14;
}

.extract-note p {
  margin: 6px 0;
  line-height: 1.4;
}

.extract-actions {
  margin-top: 20px;
  text-align: center;
}

.download-actions {
  margin-top: 25px;
  text-align: right;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* 添加响应式设计 */
@media (max-width: 768px) {
  .package-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }

  .package-card {
    height: 260px;
  }

  .package-cover {
    height: 120px;
  }

  .package-info {
    padding: 12px 14px;
  }

  .package-title-display {
    font-size: 14px;
  }

  .action-button {
    height: 32px !important;
    font-size: 13px !important;
  }

  .dual-buttons .el-button {
    height: 32px !important;
    font-size: 13px !important;
  }
}

/* 超小屏幕 */
@media (max-width: 480px) {
  .library-container {
    padding: 15px;
  }

  .package-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .package-card {
    height: 240px;
  }

  .package-cover {
    height: 100px;
  }

  .library-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .library-title {
    font-size: 20px;
  }
}
</style>
