package models

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"school-package-system/utils"
)

// CentralAuthRequest 中央平台认证请求
type CentralAuthRequest struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	SchoolName string `json:"schoolName"`
	MacAddress string `json:"macAddress"`
}

// CentralAuthResponse 中央平台认证响应
type CentralAuthResponse struct {
	Code    string `json:"code"`
	Message string `json:"msg"`
	Data    struct {
		UserInfo struct {
			PackageSystemSchoolId string                 `json:"packageSystemSchoolId"`
			CreateTime            int64                  `json:"createTime"`
			ExtraInfo             map[string]interface{} `json:"extraInfo"`
			SchoolId              string                 `json:"schoolId"`
			Username              string                 `json:"username"`
			HasLogin              int                    `json:"hasLogin"`
			Disabled              int                    `json:"disabled"`
			BindMacList           []string               `json:"bindMacList"`
		} `json:"userInfo"`
		SchoolInfo struct {
			Name string `json:"name"`
			ID   string `json:"id"`
		} `json:"schoolInfo"`
		Token string `json:"token"`
	} `json:"data"`
}

// CentralTokenValidationResponse 中央平台令牌验证响应
type CentralTokenValidationResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Valid     bool      `json:"valid"`
		ExpiresAt time.Time `json:"expiresAt"`
		User      struct {
			Username string `json:"username"`
			SchoolID string `json:"schoolId"`
		} `json:"user"`
	} `json:"data"`
}

// AuthenticateWithCentral 与中央平台进行认证
func AuthenticateWithCentral(username, password, schoolName string) (string, string, string, time.Time, error) {
	centralAPIURL := os.Getenv("CENTRAL_API_URL")
	if centralAPIURL == "" {
		return "", "", "", time.Time{}, errors.New("central API URL not configured")
	}

	authURL := fmt.Sprintf("%s/v1/packageSystem/packageSystemSchool/login", centralAPIURL)

	// 获取MAC地址
	macAddress, err := utils.GetMacAddress()
	if err != nil {
		return "", "", "", time.Time{}, fmt.Errorf("获取MAC地址失败: %v", err)
	}

	// 准备请求体
	authRequest := CentralAuthRequest{
		Username:   username,
		Password:   password,
		SchoolName: schoolName,
		MacAddress: macAddress,
	}

	requestBody, err := json.Marshal(authRequest)
	if err != nil {
		return "", "", "", time.Time{}, err
	}

	// 发送请求
	client := &http.Client{
		Timeout: time.Duration(30) * time.Second,
	}

	resp, err := client.Post(authURL, "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return "", "", "", time.Time{}, err
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", "", time.Time{}, err
	}

	//// 打印完整的响应体
	fmt.Printf("中央平台认证响应体: %s\n", string(respBody))

	// 解析响应
	var authResponse CentralAuthResponse
	if err := json.Unmarshal(respBody, &authResponse); err != nil {
		return "", "", "", time.Time{}, err
	}

	// 检查响应状态
	if authResponse.Code != "000000" {
		return "", "", "", time.Time{}, fmt.Errorf("authentication failed: %s", authResponse.Message)
	}

	//// 打印调试信息
	//fmt.Printf("中央平台认证响应: Token=%s, SchoolID=%s, SchoolName=%s\n",
	//	authResponse.Data.Token,
	//	authResponse.Data.SchoolInfo.ID,
	//	authResponse.Data.SchoolInfo.Name)

	// 由于中央端返回的token是长期有效的，我们设置一个较长的过期时间（例如10年）
	expiresAt := time.Now().AddDate(10, 0, 0)

	// 返回令牌、学校ID、学校名称和过期时间
	return authResponse.Data.Token,
		authResponse.Data.SchoolInfo.ID,
		authResponse.Data.SchoolInfo.Name,
		expiresAt,
		nil
}

// ValidateCentralToken 验证中央平台令牌
// 由于中央端token是长期有效的，此方法简化为直接返回有效
func ValidateCentralToken(token string) (bool, error) {
	// 如果token不为空，则认为有效
	if token == "" {
		return false, errors.New("token is empty")
	}

	// 由于中央端token是长期有效的，我们假设所有非空token都是有效的
	return true, nil
}

// RefreshCentralToken 刷新中央平台令牌
// 由于中央端token是长期有效的，此方法简化为直接返回原token
func RefreshCentralToken(oldToken string) (string, time.Time, error) {
	// 如果token为空，返回错误
	if oldToken == "" {
		return "", time.Time{}, errors.New("token is empty")
	}

	// 由于中央端token是长期有效的，我们直接返回原token和一个较长的过期时间
	expiresAt := time.Now().AddDate(10, 0, 0)
	return oldToken, expiresAt, nil
}
