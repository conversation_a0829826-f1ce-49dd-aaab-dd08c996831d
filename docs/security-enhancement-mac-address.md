# 学校登录安全性增强 - MAC地址验证和登录日志

## 概述

为了增加系统安全性，我们对学校端登录功能进行了以下增强：

1. **MAC地址验证**：登录时需要提交客户端MAC地址，系统会记录和验证绑定的MAC地址列表
2. **登录日志记录**：记录每次登录尝试的详细信息，包括成功和失败的登录

## 修改内容

### 1. 客户端修改（teacher-client 和 backend）

#### 修改文件：
- `teacher-client/models/central_auth.go`
- `backend/models/central_auth.go`

#### 主要变更：
1. **CentralAuthRequest结构体**：添加了`MacAddress`字段
2. **AuthenticateWithCentral函数**：
   - 自动获取本机MAC地址
   - 在登录请求中包含MAC地址信息

```go
type CentralAuthRequest struct {
    Username   string `json:"username"`
    Password   string `json:"password"`
    SchoolName string `json:"schoolName"`
    MacAddress string `json:"macAddress"`  // 新增
}
```

### 2. 中央端修改（java-erp）

#### 新增文件：
1. **实体类**：`src/main/kotlin/com/cdzyhd/erp/entity/packageSystem/PackageSystemSchoolLoginLogEntity.java`
2. **Repository**：`src/main/kotlin/com/cdzyhd/erp/repository/packageSystem/PackageSystemSchoolLoginLogRepository.java`
3. **Controller**：`src/main/kotlin/com/cdzyhd/erp/controller/v1/packageSystem/PackageSystemSchoolLoginLogController.kt`
4. **前端模型**：`src/model/packageSystem/PackageSystemSchoolLoginLogModel.js`
5. **前端API**：`src/api/packageSystem/PackageSystemSchoolLoginLogApi.js`

#### 修改文件：
1. **登录控制器**：`src/main/kotlin/com/cdzyhd/erp/controller/v1/packageSystem/PackageSystemSchoolController.kt`
2. **静态Bean**：`src/main/kotlin/com/cdzyhd/erp/a/StaticBean.java`

### 3. 登录日志实体字段

```java
public class PackageSystemSchoolLoginLogEntity {
    public String packageSystemSchoolLoginLogId;    // 日志ID
    public String packageSystemSchoolId;            // 学校用户ID
    public String schoolId;                         // 学校ID
    public String username;                         // 登录用户名
    public String schoolName;                       // 学校名称
    public String macAddress;                       // 登录MAC地址
    public String ipAddress;                        // 登录IP地址
    public String loginStatus;                      // 登录状态 (success/failed)
    public String failureReason;                    // 登录失败原因
    public String userAgent;                        // 用户代理信息
    public Long loginTime;                          // 登录时间戳
    public Long createTime;                         // 创建时间
    public JSONObject extraInfo;                    // 扩展信息
}
```

### 4. MAC地址验证逻辑

1. **首次登录**：如果学校用户的`bindMacList`为空，系统会自动将当前MAC地址添加到绑定列表
2. **后续登录**：系统会检查当前MAC地址是否在绑定列表中，如果不在则拒绝登录
3. **登录日志**：无论登录成功还是失败，都会记录详细的登录日志

### 5. 新增API接口

#### 登录日志管理接口：
- `POST /v1/packageSystem/packageSystemSchoolLoginLog/list` - 获取登录日志列表（不分页）
- `POST /v1/packageSystem/packageSystemSchoolLoginLog/pageList` - 获取登录日志列表（分页）
- `POST /v1/packageSystem/packageSystemSchoolLoginLog/getOne` - 获取单个登录日志
- `POST /v1/packageSystem/packageSystemSchoolLoginLog/delete` - 删除登录日志
- `GET /v1/packageSystem/packageSystemSchoolLoginLog/getBySchoolUserId` - 根据学校用户ID获取登录日志
- `GET /v1/packageSystem/packageSystemSchoolLoginLog/getBySchoolId` - 根据学校ID获取登录日志
- `GET /v1/packageSystem/packageSystemSchoolLoginLog/getByMacAddress` - 根据MAC地址获取登录日志
- `GET /v1/packageSystem/packageSystemSchoolLoginLog/getLoginStats` - 获取登录统计信息

## 安全特性

### 1. MAC地址绑定
- 每个学校账号可以绑定多个MAC地址
- 首次登录自动绑定当前设备MAC地址
- 未绑定的设备无法登录，需要管理员手动添加

### 2. 登录日志记录
- 记录所有登录尝试（成功和失败）
- 包含详细的设备信息（MAC地址、IP地址、用户代理）
- 记录失败原因，便于安全审计

### 3. IP地址获取
- 支持代理服务器环境下的真实IP获取
- 检查多种HTTP头部字段（X-Forwarded-For、Proxy-Client-IP等）

## 使用说明

### 1. 管理员操作
- 可以通过管理界面查看所有登录日志
- 可以根据学校、用户、MAC地址等条件筛选日志
- 可以手动管理用户的MAC地址绑定列表

### 2. 用户体验
- 首次登录会自动绑定当前设备
- 在新设备上登录会被拒绝，需要联系管理员
- 登录失败会显示明确的错误信息

## 数据库集合

新增MongoDB集合：`packageManager_packageSystemSchoolLoginLog`

## 注意事项

1. **向后兼容性**：现有的学校账号在首次使用新版本登录时会自动绑定MAC地址
2. **错误处理**：登录日志记录失败不会影响正常的登录流程
3. **性能考虑**：登录日志采用异步记录，不影响登录响应时间
4. **隐私保护**：MAC地址等敏感信息仅用于安全验证，不会泄露给第三方

## 测试验证

所有修改的代码都已通过编译测试：
- ✅ teacher-client Go代码编译通过
- ✅ backend Go代码编译通过  
- ✅ Java/Kotlin代码语法检查通过
- ✅ 前端JavaScript代码结构正确
